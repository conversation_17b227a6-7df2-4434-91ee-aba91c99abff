# Get your OpenAI API Key here for chat models: https://platform.openai.com/account/api-keys
OPENAI_API_KEY=****

# Get your Fireworks AI API Key here for reasoning models: https://fireworks.ai/account/api-keys
FIREWORKS_API_KEY=****

# Get your DeepSeek API Key here: https://www.volcengine.com/experience/ark?utm_term=202502dsinvite&ac=DSASUQY5&rc=S5JCRGKP
OPENAI_COMPLETIONS_BASE_URL=****
OPENAI_COMPLETIONS_API_KEY=****

OPENAI_COMPLETIONS_MODEL_SMALL=****
OPENAI_COMPLETIONS_MODEL_LARGE=****
OPENAI_COMPLETIONS_MODEL_REASONING=****
OPENAI_COMPLETIONS_MODEL_FUNCTION=****


# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=****

# OAuth Providers
# Google OAuth - Get from https://console.developers.google.com/
GOOGLE_CLIENT_ID=****
GOOGLE_CLIENT_SECRET=****

# GitHub OAuth - Get from https://github.com/settings/applications/new
GITHUB_CLIENT_ID=****
GITHUB_CLIENT_SECRET=****

# The following keys below are automatically created and
# added to your environment when you deploy on vercel

# Instructions to create a Vercel Blob Store here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=****

# Instructions to create a database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=****
